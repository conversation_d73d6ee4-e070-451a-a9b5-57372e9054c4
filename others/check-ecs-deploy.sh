#!/bin/bash

IMAGE_URI="-----Image URI-----"
SERVICE_NAME="gateway"
ECS_CLUSTER="magie-staging-cluster"

echo "Starting deployment verification for service $SERVICE_NAME in cluster $ECS_CLUSTER"

# Get the latest active task definition ARN
LATEST_TASK_DEF=$(aws ecs list-task-definitions --family-prefix $SERVICE_NAME --sort DESC --status ACTIVE --query "taskDefinitionArns[0]" --output text)
echo "Latest active task definition: $LATEST_TASK_DEF"

# Function to check if all tasks are running the latest version
check_tasks() {
    echo "$(date): Checking all running tasks..."

    # Get all running tasks for the service
    TASKS=$(aws ecs list-tasks --cluster $ECS_CLUSTER --service-name $SERVICE_NAME --output json)
    TASK_ARNS=$(echo $TASKS | jq -r '.taskArns[]')

    if [ -z "$TASK_ARNS" ]; then
        echo "No running tasks found."
        return 1
    fi

    # Check each task
    ALL_TASKS_UPDATED=true

    for TASK_ARN in $TASK_ARNS; do
        TASK_INFO=$(aws ecs describe-tasks --cluster $ECS_CLUSTER --tasks $TASK_ARN --output json)

        # Get task definition and status
        TASK_DEF=$(echo $TASK_INFO | jq -r '.tasks[0].taskDefinitionArn')
        TASK_STATUS=$(echo $TASK_INFO | jq -r '.tasks[0].lastStatus')

        # Get container image
        RUNNING_IMAGE=$(echo $TASK_INFO | jq -r --arg SERVICE_NAME "$SERVICE_NAME" '.tasks[0].containers[] | select(.name == $SERVICE_NAME).image')

        echo "Task: $TASK_ARN"
        echo "  - Status: $TASK_STATUS"
        echo "  - Task Definition: $TASK_DEF"
        echo "  - Image: $RUNNING_IMAGE"

        # Check if this task is running the latest task definition
        if [ "$TASK_DEF" != "$LATEST_TASK_DEF" ]; then
            ALL_TASKS_UPDATED=false
            echo "  - Not running the latest task definition"
        else
            echo "  - Running the latest task definition ✓"
        fi
    done

    return $ALL_TASKS_UPDATED
}

# Keep checking until all tasks are updated or timeout
TIMEOUT=600  # 10 minutes timeout
START_TIME=$(date +%s)

while true; do
if check_tasks; then
    echo "All tasks are now running the expected image: $IMAGE_URI"
    exit 0
fi

# Check for timeout
CURRENT_TIME=$(date +%s)
ELAPSED_TIME=$((CURRENT_TIME - START_TIME))

if [ $ELAPSED_TIME -gt $TIMEOUT ]; then
    echo "Timeout reached. Not all tasks are running the expected image."
    exit 1
fi

# echo "Waiting 30 seconds before checking again..."
sleep 30
done